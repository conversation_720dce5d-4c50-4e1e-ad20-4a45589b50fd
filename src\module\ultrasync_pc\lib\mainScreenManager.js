/**
 * MainScreen连接管理器
 * 负责初始化MainScreen连接、处理socket连接状态、管理重连逻辑等
 */
import ServiceConfig from '@/common/ServiceConfig.js'
import CMainScreen from './CMainScreen'
import CAiEngineer from './CAiEngineer'
import CFileTransferAssistanter from './CFileTransferAssistanter'
import CCentralStationProvider from './CCentralStationProvider'
import CCentralStationUser from './CCentralStationUser'
import CFeedbackQuestionAssistanter from './CFeedbackQuestionAssistanter'
import { getSocketServer } from './common_base'
import service from '../service/service'

class MainScreenManager {
    constructor(vueInstance) {
        this.vm = vueInstance
        this.mainScreen = null
        this.controller = null
    }

    /**
     * 初始化MainScreen
     */
    initMainScreen() {
        console.log('[MainScreenManager] initMainScreen')

        const socketServer = getSocketServer()
        const option = {
            uid: this.vm.user.id,
            url: socketServer,
            client_uuid: this.vm.user.client_uuid,
            client_type: this.vm.systemConfig.clientType,
            service_type: this.vm.user.service_type
        }

        // 创建MainScreen实例
        window.main_screen = this.newMainScreen(option)
        this.mainScreen = window.main_screen
        this.controller = window.main_screen.controller

        // 初始化控制器
        this.controller.init(this.vm)

        // 初始化设备故障映射
        this.initDeviceFailureMap()

        return this.controller
    }

    /**
     * 创建MainScreen实例
     * @param {Object} option 配置选项
     * @returns {Object} MainScreen实例
     */
    newMainScreen(option) {
        let main_screen = null

        if (ServiceConfig.type.AiAnalyze === option.service_type || ServiceConfig.type.DrAiAnalyze === option.service_type) {
            main_screen = new CAiEngineer(option)
        } else if (ServiceConfig.type.FileTransferAssistant === option.service_type) {
            main_screen = new CFileTransferAssistanter(option)
        } else if (ServiceConfig.type.CentralStation === option.service_type) {
            main_screen = new CCentralStationProvider(option)
        } else if (ServiceConfig.type.CentralStationUser === option.service_type) {
            main_screen = new CCentralStationUser(option)
        } else if (ServiceConfig.type.FeedbackQuestionAssistant === option.service_type) {
            main_screen = new CFeedbackQuestionAssistanter(option)
        } else {
            main_screen = new CMainScreen(option)
        }

        return main_screen
    }

    /**
     * Socket连接成功处理
     */
    onSocketConnectSuccess() {
        console.log('[MainScreenManager] socket connect success')

        this.vm.$store.commit('loadingConfig/updateLoaded', {
            key: 'networkUnavailable',
            loaded: false
        })
    }

    /**
     * Socket重连中处理
     */
    onSocketReconnecting() {
        console.log('[MainScreenManager] socket reconnecting')

        this.vm.$store.commit('loadingConfig/updateLoaded', {
            key: 'networkUnavailable',
            loaded: true
        })
    }

    /**
     * Socket重连成功处理
     */
    onSocketReconnect() {
        console.log('[MainScreenManager] socket reconnect success')

        this.vm.$store.commit('loadingConfig/updateLoaded', {
            key: 'networkUnavailable',
            loaded: false
        })
        this.vm.$store.commit('notifications/clearFriendApply');
        this.vm.$store.commit('notifications/clearGroupApply');
    }

    /**
     * Socket重连失败处理
     * @param {Object} error 错误信息
     */
    onSocketReconnectFail(error) {
        console.log('[MainScreenManager] socket reconnect fail:', error)
        this.vm.resetApp()
    }

    /**
     * Socket断开连接处理
     * @param {Object} data 断开数据
     */
    onSocketDisconnect(data) {
        console.log('[MainScreenManager] socket disconnect:', data)

        this.vm.$store.commit('loadingConfig/updateLoaded', {
            key: 'networkUnavailable',
            loaded: true
        })

        this.vm.clearLiveStatus()
    }

    /**
     * Socket错误处理
     * @param {string} error 错误信息
     */
    onSocketError(error) {
        console.warn('[MainScreenManager] socket error:', error)

    }

    /**
     * 解绑控制器事件
     */
    unBindControllerEvent() {
        if (window.main_screen && window.main_screen.controller) {
            const controller = window.main_screen.controller

            // 解绑网关相关事件
            controller.off("gateway_connect")
            controller.off("gateway_error")
            controller.off("gateway_reconnecting")
            controller.off("gateway_reconnect_fail")
            controller.off("gateway_reconnect")
            controller.off("gateway_disconnect")

            // 解绑MainScreen相关事件
            controller.off("recent_active_conversation_list")
            controller.off("recent_active_conversation_list_last_message")
            controller.off("friend_list")
            controller.off("userAddLoginClient")
            controller.off("conversation_list")
            controller.off("group_applys")
            controller.off("friend_applys")
            controller.off("userResponseFriendApply")
            controller.off("userAddFriend")
            controller.off("userApplyAddFriend")
            controller.off("notify_add_friend")
            controller.off("update_friend_info")
            controller.off("notify_friend_destroy")
            controller.off("update_user_info")
            controller.off("update_user_portrait_img")
            controller.off("notify_start_conversation")
            controller.off("notify_login_another")
            controller.off("notify_user_destroy")
            controller.off("server_info")
            controller.off("notify_delete_group")
            controller.off("open_register_scan_room_view")
            controller.off("user_info")
            controller.off("version_info")
            controller.off("request_conversation_start_ultrasound_desktop")
            controller.off("notify_download_task")
            controller.off("notify_update_groupset_portrait")
            controller.off("notify_update_media_transfer_task")
            controller.off("notify_delete_media_transfer_task")
            controller.off("notify_exception")
            controller.off("NotifyStandaloneWorkstationShareExamInfo")
            controller.off("receive_group_message")
            controller.off("notify_agora_live_start")
            controller.off("notify_agora_live_stop")
            controller.off("notify_update_recording")
            controller.off("notify_update_announcement")
            controller.off("notify.group.resource.delete.exam")
            controller.off("notify.group.resource.delete.resource")
            controller.off("notify_refresh_manager_groupset_list")
            controller.off("notify_refresh_my_groupset_list")
            controller.off("update_ai_analyze_report")
            controller.off("student_answer_sheet_update")
            controller.off("teacher_answer_sheet_update")
            controller.off("equipment_server_device_alram_update")
        }
    }

    /**
     * 关闭Socket连接
     */
    closeSocket() {
        if (window.main_screen && window.main_screen.gateway) {
            window.main_screen.CloseSocket()
        }
    }

    /**
     * 获取控制器实例
     * @returns {Object} 控制器实例
     */
    getController() {
        return this.controller
    }

    /**
     * 获取MainScreen实例
     * @returns {Object} MainScreen实例
     */
    getMainScreen() {
        return this.mainScreen
    }

    /**
     * 检查连接状态
     * @returns {boolean} 是否已连接
     */
    isConnected() {
        return window.main_screen && window.main_screen.gateway && window.main_screen.gateway.connected
    }

    /**
     * 清理资源
     */
    destroy() {
        this.unBindControllerEvent()
        this.closeSocket()
        this.mainScreen = null
        this.controller = null
        window.main_screen = null
    }

    // 初始化设备故障映射
    initDeviceFailureMap(){
        this.vm.$store.commit('device/updateDeviceInfo',{
            deviceFailure:{}
        })
        service.getNewDeviceFailure({series_numbers:[]}).then(res=>{
            if (res.data.error_code===0) {
                const deviceFailure = this.vm.$store.state.device.deviceFailure;
                for(let device_id in res.data.data){
                    let num = res.data.data[device_id].length || 0;
                    const obj = {}
                    obj[device_id] = num;
                    this.vm.$store.commit('device/updateDeviceFailure',obj);
                }
            }
        })
    }
}

export default MainScreenManager
